Caused by: org.apache.flink.table.api.SqlParserException: SQL parse failed. Encountered "ref" at line 85, column 5. Was expecting one of: "ABS" ... "ALL" ... "ARRAY" ... "ARRAY_AGG" ... "ARRAY_CONCAT_AGG" ... "AVG" ... "CARDINALITY" ... "CASE" ... "CAST" ... "CEIL" ... "CEILING" ... "CHAR_LENGTH" ... "CHARACTER_LENGTH" ... "CLASSIFIER" ... "COALESCE" ... "COLLECT" ... "CONVERT" ... "COUNT" ... "COVAR_POP" ... "COVAR_SAMP" ... "CUME_DIST" ... "CURRENT" ... "CURRENT_CATALOG" ... "CURRENT_DATE" ... "CURRENT_DEFAULT_TRANSFORM_GROUP" ... "CURRENT_PATH" ... "CURRENT_ROLE" ... "CURRENT_SCHEMA" ... "CURRENT_TIME" ... "CURRENT_TIMESTAMP" ... "CURRENT_USER" ... "CURSOR" ... "DATE" ... "DENSE_RANK" ... "DISTINCT" ... "ELEMENT" ... "EVERY" ... "EXISTS" ... "EXP" ... "EXTRACT" ... "FALSE" ... "FIRST_VALUE" ... "FLOOR" ... "FUSION" ... "GROUP_CONCAT" ... "GROUPING" ... "HOUR" ... "INTERSECTION" ... "INTERVAL" ... "JSON_ARRAY" ... "JSON_ARRAYAGG" ... "JSON_EXISTS" ... "JSON_OBJECT" ... "JSON_OBJECTAGG" ... "JSON_QUERY" ... "JSON_VALUE" ... "LAG" ... "LAST_VALUE" ... "LEAD" ... "LEFT" ... "LN" ... "LOCALTIME" ... "LOCALTIMESTAMP" ... "LOWER" ... "MATCH_NUMBER" ... "MAX" ... "MIN" ... "MINUTE" ... "MOD" ... "MONTH" ... "MULTISET" ... "NEW" ... "NEXT" ... "NOT" ... "NTH_VALUE" ... "NTILE" ... "NULL" ... "NULLIF" ... "OCTET_LENGTH" ... "OVERLAY" ... "PERCENTILE_CONT" ... "PERCENTILE_DISC" ... "PERCENT_RANK" ... "PERIOD" ... "POSITION" ... "POWER" ... "PREV" ... "RANK" ... "REGR_COUNT" ... "REGR_SXX" ... "REGR_SYY" ... "RIGHT" ... "ROW" ... "ROW_NUMBER" ... "RUNNING" ... "SECOND" ... "SESSION_USER" ... "SOME" ... "SPECIFIC" ... "SQRT" ... "STDDEV_POP" ... "STDDEV_SAMP" ... "STREAM" ... "STRING_AGG" ... "SUBSTRING" ... "SUM" ... "SYSTEM_USER" ... "TIME" ... "TIMESTAMP" ... "TRANSLATE" ... "TRIM" ... "TRUE" ... "TRUNCATE" ... "UNIQUE" ... "UNKNOWN" ... "UPPER" ... "USER" ... "VAR_POP" ... "VAR_SAMP" ... "YEAR" ... <UNSIGNED_INTEGER_LITERAL> ... <APPROX_NUMERIC_LITERAL> ... <DECIMAL_NUMERIC_LITERAL> ... <BINARY_STRING_LITERAL> ... <QUOTED_STRING> ... <PREFIXED_STRING_LITERAL> ... <UNICODE_STRING_LITERAL> ... <BIG_QUERY_DOUBLE_QUOTED_STRING> ... <BIG_QUERY_QUOTED_STRING> ... "(" ... <LBRACE_D> ... <LBRACE_T> ... <LBRACE_TS> ... <LBRACE_FN> ... "?" ... "+" ... "-" ... "*" ... "/*+" ... <BRACKET_QUOTED_IDENTIFIER> ... <QUOTED_IDENTIFIER> ... <BACK_QUOTED_IDENTIFIER> ... <BIG_QUERY_BACK_QUOTED_IDENTIFIER> ... <HYPHENATED_IDENTIFIER> ... <IDENTIFIER> ... <UNICODE_QUOTED_IDENTIFIER> ... at org.apache.flink.table.planner.parse.CalciteParser.parse(CalciteParser.java:62) ... 149 more Caused by: org.apache.calcite.sql.parser.SqlParseException: Encountered "ref" at line 85, column 5. Was expecting one of: "ABS" ... "ALL" ... "ARRAY" ... "ARRAY_AGG" ... "ARRAY_CONCAT_AGG" ... "AVG" ... "CARDINALITY" ... "CASE" ... "CAST" ... "CEIL" ... "CEILING" ... "CHAR_LENGTH" ... "CHARACTER_LENGTH" ... "CLASSIFIER" ... "COALESCE" ... "COLLECT" ... "CONVERT" ... "COUNT" ... "COVAR_POP" ... "COVAR_SAMP" ... "CUME_DIST" ... "CURRENT" ... "CURRENT_CATALOG" ... "CURRENT_DATE" ... "CURRENT_DEFAULT_TRANSFORM_GROUP" ... "CURRENT_PATH" ... "CURRENT_ROLE" ... "CURRENT_SCHEMA" ... "CURRENT_TIME" ... "CURRENT_TIMESTAMP" ... "CURRENT_USER" ... "CURSOR" ... "DATE" ... "DENSE_RANK" ... "DISTINCT" ... "ELEMENT" ... "EVERY" ... "EXISTS" ... "EXP" ... "EXTRACT" ... "FALSE" ... "FIRST_VALUE" ... "FLOOR" ... "FUSION" ... "GROUP_CONCAT" ... "GROUPING" ... "HOUR" ... "INTERSECTION" ... "INTERVAL" ... "JSON_ARRAY" ... "JSON_ARRAYAGG" ... "JSON_EXISTS" ... "JSON_OBJECT" ... "JSON_OBJECTAGG" ... "JSON_QUERY" ... "JSON_VALUE" ... "LAG" ... "LAST_VALUE" ... "LEAD" ... "LEFT" ... "LN" ... "LOCALTIME" ... "LOCALTIMESTAMP" ... "LOWER" ... "MATCH_NUMBER" ... "MAX" ... "MIN" ... "MINUTE" ... "MOD" ... "MONTH" ... "MULTISET" ... "NEW" ... "NEXT" ... "NOT" ... "NTH_VALUE" ... "NTILE" ... "NULL" ... "NULLIF" ... "OCTET_LENGTH" ... "OVERLAY" ... "PERCENTILE_CONT" ... "PERCENTILE_DISC" ... "PERCENT_RANK" ... "PERIOD" ... "POSITION" ... "POWER" ... "PREV" ... "RANK" ... "REGR_COUNT" ... "REGR_SXX" ... "REGR_SYY" ... "RIGHT" ... "ROW" ... "ROW_NUMBER" ... "RUNNING" ... "SECOND" ... "SESSION_USER" ... "SOME" ... "SPECIFIC" ... "SQRT" ... "STDDEV_POP" ... "STDDEV_SAMP" ... "STREAM" ... "STRING_AGG" ... "SUBSTRING" ... "SUM" ... "SYSTEM_USER" ... "TIME" ... "TIMESTAMP" ... "TRANSLATE" ... "TRIM" ... "TRUE" ... "TRUNCATE" ... "UNIQUE" ... "UNKNOWN" ... "UPPER" ... "USER" ... "VAR_POP" ... "VAR_SAMP" ... "YEAR" ... <UNSIGNED_INTEGER_LITERAL> ... <APPROX_NUMERIC_LITERAL> ... <DECIMAL_NUMERIC_LITERAL> ... <BINARY_STRING_LITERAL> ... <QUOTED_STRING> ... <PREFIXED_STRING_LITERAL> ... <UNICODE_STRING_LITERAL> ... <BIG_QUERY_DOUBLE_QUOTED_STRING> ... <BIG_QUERY_QUOTED_STRING> ... "(" ... <LBRACE_D> ... <LBRACE_T> ... <LBRACE_TS> ... <LBRACE_FN> ... "?" ... "+" ... "-" ... "*" ... "/*+" ... <BRACKET_QUOTED_IDENTIFIER> ... <QUOTED_IDENTIFIER> ... <BACK_QUOTED_IDENTIFIER> ... <BIG_QUERY_BACK_QUOTED_IDENTIFIER> ... <HYPHENATED_IDENTIFIER> ... <IDENTIFIER> ... <UNICODE_QUOTED_IDENTIFIER> ... at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.convertException(FlinkSqlParserImpl.java:490) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.normalizeException(FlinkSqlParserImpl.java:254) at org.apache.calcite.sql.parser.SqlParser.handleException(SqlParser.java:145) at org.apache.calcite.sql.parser.SqlParser.parseQuery(SqlParser.java:160) at org.apache.calcite.sql.parser.SqlParser.parseStmt(SqlParser.java:185) at org.apache.flink.table.planner.parse.CalciteParser.parse(CalciteParser.java:57) ... 149 more Caused by: org.apache.flink.sql.parser.impl.ParseException: Encountered "ref" at line 85, column 5. Was expecting one of: "ABS" ... "ALL" ... "ARRAY" ... "ARRAY_AGG" ... "ARRAY_CONCAT_AGG" ... "AVG" ... "CARDINALITY" ... "CASE" ... "CAST" ... "CEIL" ... "CEILING" ... "CHAR_LENGTH" ... "CHARACTER_LENGTH" ... "CLASSIFIER" ... "COALESCE" ... "COLLECT" ... "CONVERT" ... "COUNT" ... "COVAR_POP" ... "COVAR_SAMP" ... "CUME_DIST" ... "CURRENT" ... "CURRENT_CATALOG" ... "CURRENT_DATE" ... "CURRENT_DEFAULT_TRANSFORM_GROUP" ... "CURRENT_PATH" ... "CURRENT_ROLE" ... "CURRENT_SCHEMA" ... "CURRENT_TIME" ... "CURRENT_TIMESTAMP" ... "CURRENT_USER" ... "CURSOR" ... "DATE" ... "DENSE_RANK" ... "DISTINCT" ... "ELEMENT" ... "EVERY" ... "EXISTS" ... "EXP" ... "EXTRACT" ... "FALSE" ... "FIRST_VALUE" ... "FLOOR" ... "FUSION" ... "GROUP_CONCAT" ... "GROUPING" ... "HOUR" ... "INTERSECTION" ... "INTERVAL" ... "JSON_ARRAY" ... "JSON_ARRAYAGG" ... "JSON_EXISTS" ... "JSON_OBJECT" ... "JSON_OBJECTAGG" ... "JSON_QUERY" ... "JSON_VALUE" ... "LAG" ... "LAST_VALUE" ... "LEAD" ... "LEFT" ... "LN" ... "LOCALTIME" ... "LOCALTIMESTAMP" ... "LOWER" ... "MATCH_NUMBER" ... "MAX" ... "MIN" ... "MINUTE" ... "MOD" ... "MONTH" ... "MULTISET" ... "NEW" ... "NEXT" ... "NOT" ... "NTH_VALUE" ... "NTILE" ... "NULL" ... "NULLIF" ... "OCTET_LENGTH" ... "OVERLAY" ... "PERCENTILE_CONT" ... "PERCENTILE_DISC" ... "PERCENT_RANK" ... "PERIOD" ... "POSITION" ... "POWER" ... "PREV" ... "RANK" ... "REGR_COUNT" ... "REGR_SXX" ... "REGR_SYY" ... "RIGHT" ... "ROW" ... "ROW_NUMBER" ... "RUNNING" ... "SECOND" ... "SESSION_USER" ... "SOME" ... "SPECIFIC" ... "SQRT" ... "STDDEV_POP" ... "STDDEV_SAMP" ... "STREAM" ... "STRING_AGG" ... "SUBSTRING" ... "SUM" ... "SYSTEM_USER" ... "TIME" ... "TIMESTAMP" ... "TRANSLATE" ... "TRIM" ... "TRUE" ... "TRUNCATE" ... "UNIQUE" ... "UNKNOWN" ... "UPPER" ... "USER" ... "VAR_POP" ... "VAR_SAMP" ... "YEAR" ... <UNSIGNED_INTEGER_LITERAL> ... <APPROX_NUMERIC_LITERAL> ... <DECIMAL_NUMERIC_LITERAL> ... <BINARY_STRING_LITERAL> ... <QUOTED_STRING> ... <PREFIXED_STRING_LITERAL> ... <UNICODE_STRING_LITERAL> ... <BIG_QUERY_DOUBLE_QUOTED_STRING> ... <BIG_QUERY_QUOTED_STRING> ... "(" ... <LBRACE_D> ... <LBRACE_T> ... <LBRACE_TS> ... <LBRACE_FN> ... "?" ... "+" ... "-" ... "*" ... "/*+" ... <BRACKET_QUOTED_IDENTIFIER> ... <QUOTED_IDENTIFIER> ... <BACK_QUOTED_IDENTIFIER> ... <BIG_QUERY_BACK_QUOTED_IDENTIFIER> ... <HYPHENATED_IDENTIFIER> ... <IDENTIFIER> ... <UNICODE_QUOTED_IDENTIFIER> ... at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.generateParseException(FlinkSqlParserImpl.java:46382) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.jj_consume_token(FlinkSqlParserImpl.java:46190) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.SelectExpression(FlinkSqlParserImpl.java:16183) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.SelectItem(FlinkSqlParserImpl.java:14963) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.SelectList(FlinkSqlParserImpl.java:14937) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.SqlSelect(FlinkSqlParserImpl.java:11404) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.LeafQuery(FlinkSqlParserImpl.java:732) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.LeafQueryOrExpr(FlinkSqlParserImpl.java:23536) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.QueryOrExpr(FlinkSqlParserImpl.java:22963) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.OrderedQueryOrExpr(FlinkSqlParserImpl.java:606) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.RichSqlInsert(FlinkSqlParserImpl.java:7954) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.SqlStmt(FlinkSqlParserImpl.java:3533) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.SqlStmtEof(FlinkSqlParserImpl.java:4144) at org.apache.flink.sql.parser.impl.FlinkSqlParserImpl.parseSqlStmtEof(FlinkSqlParserImpl.java:302) at org.apache.calcite.sql.parser.SqlParser.parseQuery(SqlParser.java:158) ... 151 more