import org.apache.flink.table.functions.AggregateFunction;

public class ConcatDistinct extends AggregateFunction<String, ConcatDistinctAccum> {
    
    public static class ConcatDistinctAccum {
        public Set<String> set = new HashSet<>();
    }

    @Override
    public ConcatDistinctAccum createAccumulator() {
        return new ConcatDistinctAccum();
    }

    public void accumulate(ConcatDistinctAccum acc, String value) {
        if (value != null) {
            acc.set.add(value);
        }
    }

    @Override
    public String getValue(ConcatDistinctAccum acc) {
        return String.join(",", acc.set);
    }
} 